{"rustc": 8210029788606052455, "features": "[\"async\", \"async-trait\", \"convert-case\", \"convert_case\", \"default\", \"ini\", \"json\", \"json5\", \"json5_rs\", \"ron\", \"rust-ini\", \"serde_json\", \"toml\", \"yaml\", \"yaml-rust2\"]", "declared_features": "[\"async\", \"async-trait\", \"convert-case\", \"convert_case\", \"default\", \"indexmap\", \"ini\", \"json\", \"json5\", \"json5_rs\", \"preserve_order\", \"ron\", \"rust-ini\", \"serde_json\", \"toml\", \"yaml\", \"yaml-rust2\"]", "target": 4953464226640322992, "profile": 1398949370558892206, "path": 10162044332273838614, "deps": [[1213098572879462490, "json5_rs", false, 1994892455989317609], [1238778183371849706, "yaml_rust2", false, 1737905453618006593], [2244620803250265856, "ron", false, 6144217870845044509], [2356429411733741858, "ini", false, 13188809833630609034], [6517602928339163454, "path<PERSON><PERSON>", false, 3966760060255182938], [9689903380558560274, "serde", false, 13112566706652002643], [11946729385090170470, "async_trait", false, 13205257525280736366], [13475460906694513802, "convert_case", false, 2135680574445499878], [14718834678227948963, "winnow", false, 3067118353005890949], [15367738274754116744, "serde_json", false, 1380134504603123757], [15609422047640926750, "toml", false, 14582975190641313541]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/config-06f1644c7fca2c1c/dep-lib-config", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}
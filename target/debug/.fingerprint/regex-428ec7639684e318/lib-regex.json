{"rustc": 8210029788606052455, "features": "[\"std\", \"unicode-case\", \"unicode-perl\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 8276155916380437441, "path": 15033791335116528145, "deps": [[555019317135488525, "regex_automata", false, 2838959641190739241], [9408802513701742484, "regex_syntax", false, 2759309778355932341]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/regex-428ec7639684e318/dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}
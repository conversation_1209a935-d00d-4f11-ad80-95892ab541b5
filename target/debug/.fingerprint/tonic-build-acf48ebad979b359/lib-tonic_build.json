{"rustc": 8210029788606052455, "features": "[\"default\", \"prost\", \"prost-build\", \"transport\"]", "declared_features": "[\"cleanup-markdown\", \"default\", \"prost\", \"prost-build\", \"transport\"]", "target": 9025750215440372010, "profile": 16572146140859760712, "path": 7934114986830911633, "deps": [[2739579679802620019, "prost_build", false, 3176279855363530206], [3060637413840920116, "proc_macro2", false, 10509650723215050728], [8549471757621926118, "prettyplease", false, 18102413892045358262], [16470553738848018267, "prost_types", false, 12950034483438410006], [17990358020177143287, "quote", false, 11786740177895675378], [18149961000318489080, "syn", false, 6474120591733677648]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tonic-build-acf48ebad979b359/dep-lib-tonic_build", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}